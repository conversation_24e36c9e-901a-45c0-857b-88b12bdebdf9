// Global variables
let playerData = {};
let cases = [];
let currentTab = 'submit-case';
let caseTypes = [];

// Update current date
function updateDate() {
    const now = new Date();
    const dateStr = now.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
    });
    document.getElementById('current-date').textContent = dateStr;
}

// Utility function for API calls
function post(type, data) {
    fetch(`https://tiger_law/${type}`, {
        method: 'post',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data || {})
    })
    .then(() => {})
    .catch(() => {});
}

// Tab switching function
function switchTab(tabName) {
    currentTab = tabName;

    // Update sidebar
    $('.sidebar-item').removeClass('active');
    $(`.sidebar-item[data-tab="${tabName}"]`).addClass('active');

    // Update content
    $('.tab-content').removeClass('active');
    $(`#${tabName}-tab`).addClass('active');

    // Load data based on tab
    if (tabName === 'my-cases') {
        loadMyCases();
    } else if (tabName === 'records') {
        loadRecords();
    }
}

// Close interface function
function closeInterface() {
    $('#container').addClass('hidden');
    post('hide');
}

// Close modal function
function closeModal() {
    $('#case-modal').addClass('hidden');
}

// Show case details modal
function showCaseDetails(caseData) {
    $('#modal-case-title').text(`Case #${caseData.id} - ${caseData.type}`);

    const detailsHtml = `
        <div class="case-details">
            <div class="case-detail">
                <div class="case-detail-label">Case ID</div>
                <div class="case-detail-value">#${caseData.id}</div>
            </div>
            <div class="case-detail">
                <div class="case-detail-label">Type</div>
                <div class="case-detail-value">${caseData.type}</div>
            </div>
            <div class="case-detail">
                <div class="case-detail-label">Status</div>
                <div class="case-detail-value">
                    <span class="case-status status-${caseData.status.toLowerCase().replace(' ', '-')}">
                        <i class="fas fa-circle"></i>
                        ${caseData.status}
                    </span>
                </div>
            </div>
            <div class="case-detail">
                <div class="case-detail-label">Submitted</div>
                <div class="case-detail-value">${new Date(caseData.timestamp).toLocaleDateString()}</div>
            </div>
        </div>
        <div class="case-description">
            <div class="case-detail-label">Description</div>
            <div class="case-detail-value">${caseData.description}</div>
        </div>
        ${caseData.notes ? `
            <div class="case-notes">
                <div class="case-detail-label">Notes</div>
                <div class="case-detail-value">${caseData.notes}</div>
            </div>
        ` : ''}
    `;

    $('#modal-case-details').html(detailsHtml);
    $('#case-modal').removeClass('hidden');
}

// Load my cases
function loadMyCases() {
    post('getMyCases');
}

// Load records
function loadRecords() {
    post('getRecords');
}

// Display cases
function displayCases(casesData) {
    cases = casesData || [];
    const casesList = $('#cases-list');
    casesList.empty();

    if (cases.length === 0) {
        casesList.append(`
            <div style="text-align: center; padding: 2rem; color: rgba(255, 255, 255, 0.6);">
                <i class="fas fa-folder-open" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                <p>No cases found.</p>
            </div>
        `);
        return;
    }

    cases.forEach(caseData => {
        const caseCard = `
            <div class="case-card" onclick="showCaseDetails(${JSON.stringify(caseData).replace(/"/g, '&quot;')})">
                <div class="case-header">
                    <div class="case-title">${caseData.type}</div>
                    <div class="case-id">#${caseData.id}</div>
                </div>
                <div class="case-details">
                    <div class="case-detail">
                        <div class="case-detail-label">Status</div>
                        <div class="case-detail-value">
                            <span class="case-status status-${caseData.status.toLowerCase().replace(' ', '-')}">
                                <i class="fas fa-circle"></i>
                                ${caseData.status}
                            </span>
                        </div>
                    </div>
                    <div class="case-detail">
                        <div class="case-detail-label">Submitted</div>
                        <div class="case-detail-value">${new Date(caseData.timestamp).toLocaleDateString()}</div>
                    </div>
                </div>
            </div>
        `;
        casesList.append(caseCard);
    });
}

// Display records (same as cases but for records tab)
function displayRecords(recordsData) {
    const recordsList = $('#records-list');
    recordsList.empty();

    if (!recordsData || recordsData.length === 0) {
        recordsList.append(`
            <div style="text-align: center; padding: 2rem; color: rgba(255, 255, 255, 0.6);">
                <i class="fas fa-archive" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                <p>No records found.</p>
            </div>
        `);
        return;
    }

    recordsData.forEach(record => {
        const recordCard = `
            <div class="case-card" onclick="showCaseDetails(${JSON.stringify(record).replace(/"/g, '&quot;')})">
                <div class="case-header">
                    <div class="case-title">${record.type}</div>
                    <div class="case-id">#${record.id}</div>
                </div>
                <div class="case-details">
                    <div class="case-detail">
                        <div class="case-detail-label">Player</div>
                        <div class="case-detail-value">${record.playerName}</div>
                    </div>
                    <div class="case-detail">
                        <div class="case-detail-label">Status</div>
                        <div class="case-detail-value">
                            <span class="case-status status-${record.status.toLowerCase().replace(' ', '-')}">
                                <i class="fas fa-circle"></i>
                                ${record.status}
                            </span>
                        </div>
                    </div>
                    <div class="case-detail">
                        <div class="case-detail-label">Submitted</div>
                        <div class="case-detail-value">${new Date(record.timestamp).toLocaleDateString()}</div>
                    </div>
                </div>
            </div>
        `;
        recordsList.append(recordCard);
    });
}

// Populate case types
function populateCaseTypes(types) {
    caseTypes = types || [];
    const select = $('#case-type');
    select.find('option:not(:first)').remove();

    caseTypes.forEach(type => {
        select.append(`<option value="${type}">${type}</option>`);
    });
}

// Initialize player data
function initializePlayerData(data) {
    playerData = data;
    $('#name').val(data.name || '');
    $('#citizenid').val(data.citizenid || '');
    $('#phone').val(data.phone || '');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = $(`
        <div class="notification notification-${type}">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `);

    $('#notification-container').append(notification);

    setTimeout(() => {
        notification.fadeOut(() => notification.remove());
    }, 5000);
}

// Show confirmation dialog
function showConfirmation(message, onConfirm) {
    const confirmModal = $(`
        <div class="modal confirmation-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirmation</h3>
                </div>
                <div class="modal-body">
                    <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.5;">${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="cancel-btn confirm-cancel">Cancel</button>
                    <button class="request-btn confirm-ok">
                        <i class="fas fa-check"></i>
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    `);

    $('body').append(confirmModal);

    // Handle confirm
    confirmModal.find('.confirm-ok').on('click', function() {
        confirmModal.remove();
        if (onConfirm) onConfirm();
    });

    // Handle cancel
    confirmModal.find('.confirm-cancel').on('click', function() {
        confirmModal.remove();
    });

    // Handle click outside
    confirmModal.on('click', function(e) {
        if (e.target === this) {
            confirmModal.remove();
        }
    });
}

// Document ready function
$(document).ready(function() {
    // Hide interface initially
    $('#container').addClass('hidden');

    // Update date
    updateDate();

    // Sidebar click handlers
    $('.sidebar-item').on('click', function() {
        const tab = $(this).data('tab');
        switchTab(tab);
    });

    // ESC key handler
    $(document).keyup(function(e) {
        if (e.key === "Escape") {
            if (!$('#case-modal').hasClass('hidden')) {
                closeModal();
            } else {
                closeInterface();
            }
        }
    });

    // Modal click outside to close
    $('#case-modal').on('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Form submissions
    $('#case-form').on('submit', function(e) {
        e.preventDefault();

        const formData = {
            type: $('#case-type').val(),
            description: $('#description').val()
        };

        if (!formData.type || !formData.description) {
            showNotification('Please fill in all required fields', 'error');
            return;
        }

        post('submitCase', formData);
    });

    $('#status-form').on('submit', function(e) {
        e.preventDefault();

        const caseId = $('#case-id').val();
        if (!caseId) {
            showNotification('Please enter a case ID', 'error');
            return;
        }

        post('checkCaseStatus', { caseId: parseInt(caseId) });
    });

    // Status update buttons
    $('#btn-approve').on('click', function() {
        const caseId = $('#case-id').val();
        const notes = $('#case-notes').val();
        post('updateCaseStatus', { caseId: parseInt(caseId), status: 'Approved', notes });
    });

    $('#btn-review').on('click', function() {
        const caseId = $('#case-id').val();
        const notes = $('#case-notes').val();
        post('updateCaseStatus', { caseId: parseInt(caseId), status: 'Under Review', notes });
    });

    $('#btn-reject').on('click', function() {
        const caseId = $('#case-id').val();
        const notes = $('#case-notes').val();
        post('updateCaseStatus', { caseId: parseInt(caseId), status: 'Rejected', notes });
    });

    // Clear records button
    $('#btn-clear-records').on('click', function() {
        showConfirmation('Are you sure you want to clear completed cases?', function() {
            post('clearCompletedCases');
        });
    });

    // Filter records
    $('#record-status').on('change', function() {
        const status = $(this).val();
        post('filterRecords', { status });
    });
});

// Message event listener
window.addEventListener("message", function(ev) {
    const event = ev.data;

    if (event.type === "show") {
        try {
            // Initialize player data
            if (event.playerData) {
                initializePlayerData(event.playerData);
            }

            // Populate case types
            if (event.caseTypes) {
                populateCaseTypes(event.caseTypes);
            }

            // Show interface
            $('#container').removeClass('hidden');

        } catch (error) {
            console.error("Error in message handler:", error);
        }
    }
    else if (event.type === "hide") {
        $('#container').addClass('hidden');
    }
    else if (event.type === "updateCases") {
        displayCases(event.cases);
    }
    else if (event.type === "updateRecords") {
        displayRecords(event.records);
    }
    else if (event.type === "showCaseStatus") {
        if (event.case) {
            $('#status-result').removeClass('hidden').html(`
                <div class="case-card">
                    <div class="case-header">
                        <div class="case-title">${event.case.type}</div>
                        <div class="case-id">#${event.case.id}</div>
                    </div>
                    <div class="case-details">
                        <div class="case-detail">
                            <div class="case-detail-label">Status</div>
                            <div class="case-detail-value">
                                <span class="case-status status-${event.case.status.toLowerCase().replace(' ', '-')}">
                                    <i class="fas fa-circle"></i>
                                    ${event.case.status}
                                </span>
                            </div>
                        </div>
                        <div class="case-detail">
                            <div class="case-detail-label">Submitted</div>
                            <div class="case-detail-value">${new Date(event.case.timestamp).toLocaleDateString()}</div>
                        </div>
                    </div>
                    <div class="case-description">
                        <div class="case-detail-label">Description</div>
                        <div class="case-detail-value">${event.case.description}</div>
                    </div>
                </div>
            `);

            // Show status actions if player has permission
            if (event.canUpdate) {
                $('#status-actions').removeClass('hidden');
            }
        } else {
            $('#status-result').removeClass('hidden').html(`
                <div style="text-align: center; padding: 2rem; color: rgba(255, 255, 255, 0.6);">
                    <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>Case not found.</p>
                </div>
            `);
        }
    }
    else if (event.type === "notification") {
        showNotification(event.message, event.notificationType || 'info');
    }
    else if (event.type === "formSubmitted") {
        $('#case-form')[0].reset();
        showNotification('Case submitted successfully!', 'success');
    }
    else if (event.type === "statusUpdated") {
        $('#status-result').addClass('hidden');
        $('#status-actions').addClass('hidden');
        $('#case-id').val('');
        $('#case-notes').val('');
        showNotification('Case status updated successfully!', 'success');
    }
});

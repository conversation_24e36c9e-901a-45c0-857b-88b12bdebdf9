local QBCore = exports['qb-core']:GetCoreObject()

-- Create database table if it doesn't exist
CreateThread(function()
    if Config.SaveCasesToDatabase then
        -- Create the table if it doesn't exist
        MySQL.query([[
            CREATE TABLE IF NOT EXISTS law_cases (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                citizenid VARCHAR(50) NOT NULL,
                case_type VARCHAR(50) NOT NULL,
                description TEXT NOT NULL,
                date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(50) DEFAULT 'Pending',
                notes TEXT
            )
        ]])

        -- Check if phone column exists, if not add it
        MySQL.query([[SHOW COLUMNS FROM law_cases LIKE 'phone']], {}, function(result)
            if result == nil or #result == 0 then
                print('Adding phone column to law_cases table')
                MySQL.query([[ALTER TABLE law_cases ADD COLUMN phone VARCHAR(50) DEFAULT '']], {})
            end
        end)

        -- Check if notes column exists, if not add it
        MySQL.query([[SHOW COLUMNS FROM law_cases LIKE 'notes']], {}, function(result)
            if result == nil or #result == 0 then
                print('Adding notes column to law_cases table')
                MySQL.query([[ALTER TABLE law_cases ADD COLUMN notes TEXT]], {})
            end
        end)
    end
end)

-- Function to send Discord webhook for new case
local function SendNewCaseWebhook(caseData)
    if not Config.UseDiscordWebhook then return end

    -- Get the appropriate webhook URL based on case type
    local webhookUrl = Config.DiscordWebhooks[caseData.caseType] or Config.DiscordWebhooks['default']
    if not webhookUrl or webhookUrl == 'YOUR_DEFAULT_WEBHOOK_URL' then
        print('Discord webhook URL not configured')
        return
    end

    -- Format the date
    local time = os.date('%Y-%m-%d %H:%M:%S')

    -- Create embed for Discord
    local embed = {
        {
            ['title'] = '**📋 NEW CASE SUBMISSION**',
            ['color'] = 9442302, -- Purple color
            ['footer'] = {
                ['text'] = 'Law Tablet | ' .. time,
                ['icon_url'] = 'https://i.imgur.com/6aPjIDn.png' -- Law icon
            },
            ['thumbnail'] = {
                ['url'] = 'https://i.imgur.com/6aPjIDn.png' -- Law icon
            },
            ['author'] = {
                ['name'] = 'Law InFiniTY CiTY',
                ['icon_url'] = 'https://i.imgur.com/6aPjIDn.png'
            },
            ['fields'] = {
                {
                    ['name'] = '**📝 Case Type**',
                    ['value'] = '```' .. GetCaseTypeLabel(caseData.caseType) .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = '**👤 Submitted By**',
                    ['value'] = '```' .. caseData.name .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = ' ', -- Space for spacing
                    ['value'] = ' ',
                    ['inline'] = false
                },
                {
                    ['name'] = '**🆔 Citizen ID**',
                    ['value'] = '```' .. caseData.citizenid .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = '**📅 Date**',
                    ['value'] = '```' .. time .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = ' ', -- Space for spacing
                    ['value'] = ' ',
                    ['inline'] = false
                },
                {
                    ['name'] = '**📄 Description**',
                    ['value'] = '```' .. caseData.description .. '```',
                    ['inline'] = false
                }
            },
        }
    }

    -- Prepare the payload
    local payload = json.encode({
        username = Config.WebhookName,
        embeds = embed,
        avatar_url = Config.WebhookAvatar
    })

    -- Send the webhook
    PerformHttpRequest(webhookUrl, function(err, text, headers) end, 'POST', payload, { ['Content-Type'] = 'application/json' })
end

-- Function to send status update webhook
local function SendStatusUpdateWebhook(caseData, newStatus, updatedBy)
    if not Config.UseDiscordWebhook then return end

    -- Get the appropriate webhook URL based on case type
    local webhookUrl = Config.DiscordWebhooks[caseData.case_type] or Config.DiscordWebhooks['default']
    if not webhookUrl or webhookUrl == 'YOUR_DEFAULT_WEBHOOK_URL' then
        print('Discord webhook URL not configured')
        return
    end

    -- Format the date
    local time = os.date('%Y-%m-%d %H:%M:%S')

    -- Create embed for Discord
    local statusColor = 9442302 -- Default purple
    if newStatus == 'Approved' then
        statusColor = 5763719 -- Green
    elseif newStatus == 'Rejected' then
        statusColor = 15548997 -- Red
    elseif newStatus == 'Under Review' then
        statusColor = 16776960 -- Yellow
    end

    local embed = {
        {
            ['title'] = '**📋 CASE STATUS UPDATE**',
            ['color'] = statusColor,
            ['footer'] = {
                ['text'] = 'Law Tablet | ' .. time,
                ['icon_url'] = 'https://i.ibb.co/cKwwCBPr/devbig.png' -- Law icon
            },
            ['thumbnail'] = {
                ['url'] = 'https://i.ibb.co/cKwwCBPr/devbig.png' -- Law icon
            },
            ['author'] = {
                ['name'] = 'Law InFiniTY CiTY',
                ['icon_url'] = 'https://i.ibb.co/cKwwCBPr/devbig.png'
            },
            ['fields'] = {
                {
                    ['name'] = '**📝 Case ID**',
                    ['value'] = '```' .. caseData.id .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = '**📃 Case Type**',
                    ['value'] = '```' .. GetCaseTypeLabel(caseData.case_type) .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = ' ', -- Space for spacing
                    ['value'] = ' ',
                    ['inline'] = false
                },
                {
                    ['name'] = '**🔴 New Status**',
                    ['value'] = '```' .. newStatus .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = '**👤 Updated By**',
                    ['value'] = '```' .. updatedBy .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = ' ', -- Space for spacing
                    ['value'] = ' ',
                    ['inline'] = false
                },
                {
                    ['name'] = '**👤 Submitted By**',
                    ['value'] = '```' .. caseData.name .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = '**📅 Submitted On**',
                    ['value'] = '```' .. caseData.date_created .. '```',
                    ['inline'] = true
                },
                {
                    ['name'] = ' ', -- Space for spacing
                    ['value'] = ' ',
                    ['inline'] = false
                },
                {
                    ['name'] = '**📝 Notes**',
                    ['value'] = '```' .. (caseData.notes or 'No notes added') .. '```',
                    ['inline'] = false
                }
            },
        }
    }

    -- Prepare the payload
    local payload = json.encode({
        username = Config.WebhookName,
        embeds = embed,
        avatar_url = Config.WebhookAvatar
    })

    -- Send the webhook
    PerformHttpRequest(webhookUrl, function(err, text, headers) end, 'POST', payload, { ['Content-Type'] = 'application/json' })
end

-- Helper function to get case type label
function GetCaseTypeLabel(caseTypeValue)
    for _, caseType in ipairs(Config.CaseTypes) do
        if caseType.value == caseTypeValue then
            return caseType.label
        end
    end
    return 'Unknown'
end

-- Helper function to check if player can manage cases
function CanManageCases(Player)
    if not Player then return false end

    local job = Player.PlayerData.job.name
    local grade = Player.PlayerData.job.grade.level

    -- Check if job is in the allowed list
    if Config.CaseManagerJobs[job] then
        -- If it's a table, check for rank requirements
        if type(Config.CaseManagerJobs[job]) == 'table' then
            if grade >= Config.CaseManagerJobs[job].rank then
                return true
            end
        else
            -- If it's just set to true, allow access
            return true
        end
    end

    return false
end

-- Event handler for case submission
RegisterNetEvent('tiger_law:server:submitCase', function(caseData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then return end

    -- Validate the data
    if not caseData.name or not caseData.citizenid or not caseData.phone or not caseData.caseType or not caseData.description then
        TriggerClientEvent('QBCore:Notify', src, 'Invalid case data', 'error')
        return
    end

    -- Send to Discord webhook
    SendNewCaseWebhook(caseData)

    -- Save to database if enabled
    if Config.SaveCasesToDatabase then
        MySQL.insert('INSERT INTO law_cases (name, citizenid, phone, case_type, description) VALUES (?, ?, ?, ?, ?)',
            {caseData.name, caseData.citizenid, caseData.phone, caseData.caseType, caseData.description},
            function(id)
                if id then
                    TriggerClientEvent('QBCore:Notify', src, 'Case submitted successfully. Case ID: ' .. id, 'success')
                else
                    TriggerClientEvent('QBCore:Notify', src, 'Failed to save case to database', 'error')
                end
            end
        )
    else
        TriggerClientEvent('QBCore:Notify', src, 'Case submitted successfully', 'success')
    end
end)

-- Callback for getting case status
QBCore.Functions.CreateCallback('tiger_law:server:getCaseStatus', function(source, cb, caseId)
    if not Config.SaveCasesToDatabase then
        cb({success = false, message = 'Case tracking is disabled'})
        return
    end

    MySQL.query('SELECT * FROM law_cases WHERE id = ?', {caseId}, function(result)
        if result and result[1] then
            -- Check if the player can manage cases
            local Player = QBCore.Functions.GetPlayer(source)
            local canManage = false

            if Player then
                canManage = CanManageCases(Player)
            end

            cb({success = true, case = result[1], canManage = canManage})
        else
            cb({success = false, message = 'Case not found'})
        end
    end)
end)

-- Callback for getting player cases
QBCore.Functions.CreateCallback('tiger_law:server:getPlayerCases', function(source, cb, citizenid)
    if not Config.SaveCasesToDatabase then
        cb({success = false, message = 'Case tracking is disabled'})
        return
    end

    MySQL.query('SELECT * FROM law_cases WHERE citizenid = ? ORDER BY date_created DESC', {citizenid}, function(result)
        if result and #result > 0 then
            cb({success = true, cases = result})
        else
            cb({success = false, message = 'No cases found'})
        end
    end)
end)

-- Callback for getting all cases (for records screen)
QBCore.Functions.CreateCallback('tiger_law:server:getAllCases', function(source, cb, data)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then return end

    -- Check if player can manage cases
    if not CanManageCases(Player) then
        cb({success = false, message = 'You do not have permission to view records'})
        return
    end

    if not Config.SaveCasesToDatabase then
        cb({success = false, message = 'Case tracking is disabled'})
        return
    end

    local query = 'SELECT * FROM law_cases ORDER BY date_created DESC'
    local params = {}

    -- Filter by status if provided
    if data and data.status and data.status ~= 'all' then
        query = 'SELECT * FROM law_cases WHERE status = ? ORDER BY date_created DESC'
        params = {data.status}
    end

    MySQL.query(query, params, function(result)
        if result and #result > 0 then
            cb({success = true, cases = result})
        else
            cb({success = false, message = 'No cases found'})
        end
    end)
end)

-- Callback for updating case status
QBCore.Functions.CreateCallback('tiger_law:server:updateCaseStatus', function(source, cb, data)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then
        cb({success = false, message = 'Player not found'})
        return
    end

    -- Check if player can manage cases
    if not CanManageCases(Player) then
        TriggerClientEvent('QBCore:Notify', src, 'You do not have permission to update case status', 'error')
        cb({success = false, message = 'You do not have permission to update case status'})
        return
    end

    -- Update case status in database
    if Config.SaveCasesToDatabase then
        MySQL.update('UPDATE law_cases SET status = ?, notes = ? WHERE id = ?', {data.status, data.notes, data.caseId}, function(affectedRows)
            if affectedRows > 0 then
                -- Send notification to player
                TriggerClientEvent('QBCore:Notify', src, 'Case status updated successfully', 'success')

                -- Get case details for webhook
                MySQL.query('SELECT * FROM law_cases WHERE id = ?', {data.caseId}, function(result)
                    if result and result[1] then
                        -- Send webhook notification about status update
                        SendStatusUpdateWebhook(result[1], data.status, Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname)
                    end
                end)

                cb({success = true, message = 'Case status updated successfully'})
            else
                TriggerClientEvent('QBCore:Notify', src, 'Failed to update case status', 'error')
                cb({success = false, message = 'Failed to update case status'})
            end
        end)
    else
        TriggerClientEvent('QBCore:Notify', src, 'Case tracking is disabled', 'error')
        cb({success = false, message = 'Case tracking is disabled'})
    end
end)

-- For backward compatibility
RegisterNetEvent('tiger_law:server:updateCaseStatus', function(caseId, newStatus, notes)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then return end

    -- Check if player can manage cases
    if not CanManageCases(Player) then
        TriggerClientEvent('QBCore:Notify', src, 'You do not have permission to update case status', 'error')
        return
    end

    -- Update case status in database
    if Config.SaveCasesToDatabase then
        MySQL.update('UPDATE law_cases SET status = ?, notes = ? WHERE id = ?', {newStatus, notes, caseId}, function(affectedRows)
            if affectedRows > 0 then
                -- Send notification to player
                TriggerClientEvent('QBCore:Notify', src, 'Case status updated successfully', 'success')

                -- Get case details for webhook
                MySQL.query('SELECT * FROM law_cases WHERE id = ?', {caseId}, function(result)
                    if result and result[1] then
                        -- Send webhook notification about status update
                        SendStatusUpdateWebhook(result[1], newStatus, Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname)
                    end
                end)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Failed to update case status', 'error')
            end
        end)
    else
        TriggerClientEvent('QBCore:Notify', src, 'Case tracking is disabled', 'error')
    end
end)

-- Callback for deleting a case
QBCore.Functions.CreateCallback('tiger_law:server:deleteCase', function(source, cb, data)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then
        cb({success = false, message = 'Player not found'})
        return
    end

    -- Check if player can manage cases
    if not CanManageCases(Player) then
        TriggerClientEvent('QBCore:Notify', src, 'You do not have permission to delete cases', 'error')
        cb({success = false, message = 'You do not have permission to delete cases'})
        return
    end

    -- Delete case from database
    if Config.SaveCasesToDatabase then
        MySQL.query('DELETE FROM law_cases WHERE id = ?', {data.caseId}, function(result)
            local affectedRows = result.affectedRows or 0
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Case deleted successfully', 'success')
                cb({success = true, message = 'Case deleted successfully'})
            else
                TriggerClientEvent('QBCore:Notify', src, 'Failed to delete case', 'error')
                cb({success = false, message = 'Failed to delete case'})
            end
        end)
    else
        TriggerClientEvent('QBCore:Notify', src, 'Case tracking is disabled', 'error')
        cb({success = false, message = 'Case tracking is disabled'})
    end
end)

-- Callback for clearing completed cases
QBCore.Functions.CreateCallback('tiger_law:server:clearCompletedCases', function(source, cb)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then
        cb({success = false, message = 'Player not found'})
        return
    end

    -- Check if player can manage cases
    if not CanManageCases(Player) then
        TriggerClientEvent('QBCore:Notify', src, 'You do not have permission to clear cases', 'error')
        cb({success = false, message = 'You do not have permission to clear cases'})
        return
    end

    -- Delete completed cases from database
    if Config.SaveCasesToDatabase then
        MySQL.query('DELETE FROM law_cases WHERE status IN ("Approved", "Rejected")', {}, function(result)
            local affectedRows = result.affectedRows or 0
            TriggerClientEvent('QBCore:Notify', src, affectedRows .. ' cases cleared successfully', 'success')
            cb({success = true, count = affectedRows, message = affectedRows .. ' cases cleared successfully'})
        end)
    else
        TriggerClientEvent('QBCore:Notify', src, 'Case tracking is disabled', 'error')
        cb({success = false, message = 'Case tracking is disabled'})
    end
end)

-- Function to check if player can manage cases is defined earlier in the file

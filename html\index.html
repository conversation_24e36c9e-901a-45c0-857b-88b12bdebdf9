<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Law System Interface</title>
    <link href="style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <!-- Main Law System Interface -->
    <div id="container" class="hidden">
        <!-- Navigation Sidebar -->
        <div class="sidebar">
            <div class="sidebar-item active" data-tab="submit-case">
                <i class="fas fa-gavel"></i>
                <i class="fas fa-gavel blur"></i>
                <span>Submit Case</span>
            </div>
            <div class="sidebar-item" data-tab="my-cases">
                <i class="fas fa-folder"></i>
                <span>My Cases</span>
            </div>
            <div class="sidebar-item" data-tab="case-status">
                <i class="fas fa-search"></i>
                <span>Case Status</span>
            </div>
            <div class="sidebar-item" data-tab="records">
                <i class="fas fa-archive"></i>
                <span>Records</span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>Law System</h1>
                <div class="header-right">
                    <div class="date-display">
                        <i class="fas fa-balance-scale"></i>
                        <span id="current-date">Loading...</span>
                    </div>
                    <button class="close-btn" onclick="closeInterface()">
                        <i class="fas fa-times"></i>
                        Close
                        <span class="esc-key">ESC</span>
                    </button>
                </div>
            </div>

            <!-- Tab Content -->
            <!-- Submit Case Tab -->
            <div id="submit-case-tab" class="tab-content active">
                <div class="submit-case-content">
                    <div class="submit-case-header">
                        <h2>Submit New Case</h2>
                        <p>Fill out the form below to submit a new legal case for review.</p>
                    </div>
                    <div class="form-container">
                        <form id="case-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name">Full Name:</label>
                                    <input type="text" id="name" name="name" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="citizenid">Citizen ID:</label>
                                    <input type="text" id="citizenid" name="citizenid" readonly>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number:</label>
                                    <input type="text" id="phone" name="phone" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="case-type">Case Type:</label>
                                    <select id="case-type" name="caseType" required>
                                        <option value="" disabled selected>Select Case Type</option>
                                        <!-- Case types will be populated from config -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="description">Case Description:</label>
                                <textarea id="description" name="description" rows="6" required placeholder="Please provide a detailed description of your case..."></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="submit-btn">
                                    <i class="fas fa-paper-plane"></i>
                                    Submit Case
                                </button>
                                <button type="reset" class="clear-btn">
                                    <i class="fas fa-eraser"></i>
                                    Clear Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- My Cases Tab -->
            <div id="my-cases-tab" class="tab-content">
                <div class="my-cases-content">
                    <div class="my-cases-header">
                        <h2>My Cases</h2>
                        <p>View and track all your submitted legal cases.</p>
                    </div>
                    <div id="cases-list" class="cases-grid">
                        <!-- Cases will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Case Status Tab -->
            <div id="case-status-tab" class="tab-content">
                <div class="case-status-content">
                    <div class="case-status-header">
                        <h2>Check Case Status</h2>
                        <p>Enter a case ID to check its current status and update it if you have permissions.</p>
                    </div>
                    <div class="status-form-container">
                        <form id="status-form">
                            <div class="form-group">
                                <label for="case-id">Case ID:</label>
                                <input type="number" id="case-id" name="caseId" required placeholder="Enter Case ID">
                            </div>
                            <button type="submit" class="check-btn">
                                <i class="fas fa-search"></i>
                                Check Status
                            </button>
                        </form>
                        <div id="status-result" class="hidden">
                            <!-- Status result will be shown here -->
                        </div>
                        <div id="status-actions" class="hidden">
                            <h3>Update Case Status</h3>
                            <div class="form-group">
                                <label for="case-notes">Notes:</label>
                                <textarea id="case-notes" name="notes" rows="3" placeholder="Add notes about this case..."></textarea>
                            </div>
                            <div class="status-buttons">
                                <button id="btn-approve" class="status-btn approve-btn">
                                    <i class="fas fa-check"></i>
                                    Approve
                                </button>
                                <button id="btn-review" class="status-btn review-btn">
                                    <i class="fas fa-eye"></i>
                                    Under Review
                                </button>
                                <button id="btn-reject" class="status-btn reject-btn">
                                    <i class="fas fa-times"></i>
                                    Reject
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Records Tab -->
            <div id="records-tab" class="tab-content">
                <div class="records-content">
                    <div class="records-header">
                        <h2>Case Records</h2>
                        <p>View and manage all case records with filtering options.</p>
                        <div class="records-actions">
                            <div class="filter-group">
                                <label for="record-status">Filter by Status:</label>
                                <select id="record-status">
                                    <option value="all">All Cases</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Under Review">Under Review</option>
                                    <option value="Approved">Approved</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                            </div>
                            <button id="btn-clear-records" class="clear-records-btn">
                                <i class="fas fa-trash"></i>
                                Clear Completed Cases
                            </button>
                        </div>
                    </div>
                    <div id="records-list" class="records-grid">
                        <!-- Records will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Case Details Modal -->
    <div id="case-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-case-title">Case Details</h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="modal-case-details">
                    <!-- Case details will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="close-modal-btn" onclick="closeModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    <div id="notification-container"></div>

    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>
    <script src="script.js"></script>
</body>
</html>

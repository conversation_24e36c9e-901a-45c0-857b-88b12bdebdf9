local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local isTabletOpen = false
local courtBlip = nil

-- Initialize player data when script starts
CreateThread(function()
    while not QBCore do
        Wait(100)
    end

    if LocalPlayer.state.isLoggedIn then
        PlayerData = QBCore.Functions.GetPlayerData()
    end

    -- Create court blip
    CreateCourtBlip()
end)

-- Initialize player data when player loads
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    -- Create court blip when player loads
    CreateCourtBlip()
end)

-- Update player data when it changes
RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

-- Command to open the tablet
RegisterNetEvent('tiger_law:client:openTablet', function()
    OpenLawTablet()
end)

-- Function to check if player has permission to use the tablet
local function HasPermission()
    if Config.AllowAllPlayers then return true end

    if PlayerData.job then
        for _, job in pairs(Config.AllowedJobs) do
            if PlayerData.job.name == job then
                return true
            end
        end
    end

    return false
end

-- Function to open the tablet
function OpenLawTablet()
    if isTabletOpen then return end

    -- Check if player has permission
    if not HasPermission() then
        QBCore.Functions.Notify('You do not have permission to use this tablet', 'error')
        return
    end

    -- Check if item is required
    if Config.ItemRequired then
        local hasItem = QBCore.Functions.HasItem(Config.RequiredItem)
        if not hasItem then
            QBCore.Functions.Notify('You need a law tablet to use this', 'error')
            return
        end
    end

    -- Make sure we have player data
    if not PlayerData or not PlayerData.citizenid then
        PlayerData = QBCore.Functions.GetPlayerData()
    end

    -- Check if charinfo exists
    if not PlayerData.charinfo then
        QBCore.Functions.Notify('Unable to load character information', 'error')
        return
    end

    -- Check if player has management permissions
    local hasManagePermission = false
    if PlayerData.job then
        hasManagePermission = CanManageCases()
    end

    -- Get player data to send to NUI
    local playerData = {
        name = PlayerData.charinfo.firstname .. ' ' .. PlayerData.charinfo.lastname,
        citizenid = PlayerData.citizenid,
        phone = PlayerData.charinfo.phone,
        caseTypes = Config.CaseTypes,
        hasManagePermission = hasManagePermission
    }

    -- Open tablet UI
    isTabletOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'show',
        playerData = playerData,
        caseTypes = Config.CaseTypes
    })

    -- Play animation for using tablet
    if Config.UISettings.animations then
        -- Try to use dpemotes if available
        if exports["dpemotes"] then
            TriggerEvent('animations:client:EmoteCommandStart', {'tablet2'})
        else
            -- Fallback animation
            TaskStartScenarioInPlace(PlayerPedId(), "WORLD_HUMAN_TABLET", 0, true)
        end
    end
end

-- NUI Callbacks
RegisterNUICallback('hide', function(_, cb)
    isTabletOpen = false
    SetNuiFocus(false, false)

    -- Stop animation if it was started
    if Config.UISettings.animations then
        -- Try to use dpemotes if available
        if exports["dpemotes"] then
            TriggerEvent('animations:client:EmoteCommandStart', {'c'})
        else
            -- Fallback animation stop
            ClearPedTasks(PlayerPedId())
        end
    end

    cb('ok')
end)

-- Submit case callback
RegisterNUICallback('submitCase', function(data, cb)
    -- Send case data to server
    TriggerServerEvent('tiger_law:server:submitCase', data)
    cb('ok')
end)

-- Event to handle case submission response
RegisterNetEvent('tiger_law:client:caseSubmitted', function(success, message)
    if success then
        SendNUIMessage({
            type = 'formSubmitted'
        })
    else
        SendNUIMessage({
            type = 'notification',
            message = message or 'Failed to submit case',
            notificationType = 'error'
        })
    end
end)

-- Get my cases callback
RegisterNUICallback('getMyCases', function(_, cb)
    if not Config.SaveCasesToDatabase then
        SendNUIMessage({
            type = 'updateCases',
            cases = {}
        })
        cb('ok')
        return
    end

    -- Request player cases from server
    QBCore.Functions.TriggerCallback('tiger_law:server:getPlayerCases', function(result)
        if result.success then
            SendNUIMessage({
                type = 'updateCases',
                cases = result.cases or {}
            })
        end
    end, PlayerData.citizenid)
    cb('ok')
end)

-- Get records callback
RegisterNUICallback('getRecords', function(_, cb)
    if not Config.SaveCasesToDatabase then
        SendNUIMessage({
            type = 'updateRecords',
            records = {}
        })
        cb('ok')
        return
    end

    -- Request all cases from server
    QBCore.Functions.TriggerCallback('tiger_law:server:getAllCases', function(result)
        if result.success then
            SendNUIMessage({
                type = 'updateRecords',
                records = result.cases or {}
            })
        end
    end, {})
    cb('ok')
end)

-- Check case status callback
RegisterNUICallback('checkCaseStatus', function(data, cb)
    if not Config.SaveCasesToDatabase then
        SendNUIMessage({
            type = 'showCaseStatus',
            case = nil,
            canUpdate = false
        })
        cb('ok')
        return
    end

    -- Request case status from server
    QBCore.Functions.TriggerCallback('tiger_law:server:getCaseStatus', function(result)
        if result.success and result.case then
            SendNUIMessage({
                type = 'showCaseStatus',
                case = result.case,
                canUpdate = CanManageCases()
            })
        else
            SendNUIMessage({
                type = 'showCaseStatus',
                case = nil,
                canUpdate = false
            })
        end
    end, data.caseId)
    cb('ok')
end)

-- Update case status callback
RegisterNUICallback('updateCaseStatus', function(data, cb)
    if not Config.SaveCasesToDatabase then
        SendNUIMessage({
            type = 'notification',
            message = 'Case tracking is disabled',
            notificationType = 'error'
        })
        cb('ok')
        return
    end

    -- Send update request to server
    QBCore.Functions.TriggerCallback('tiger_law:server:updateCaseStatus', function(result)
        if result.success then
            SendNUIMessage({
                type = 'statusUpdated'
            })
        else
            SendNUIMessage({
                type = 'notification',
                message = result.message or 'Failed to update case status',
                notificationType = 'error'
            })
        end
    end, data)
    cb('ok')
end)

-- Clear completed cases callback
RegisterNUICallback('clearCompletedCases', function(_, cb)
    if not Config.SaveCasesToDatabase then
        SendNUIMessage({
            type = 'notification',
            message = 'Case tracking is disabled',
            notificationType = 'error'
        })
        cb('ok')
        return
    end

    -- Send clear request to server
    QBCore.Functions.TriggerCallback('tiger_law:server:clearCompletedCases', function(result)
        if result.success then
            SendNUIMessage({
                type = 'notification',
                message = 'Completed cases cleared successfully',
                notificationType = 'success'
            })
            -- Refresh records
            TriggerEvent('tiger_law:client:refreshRecords')
        else
            SendNUIMessage({
                type = 'notification',
                message = result.message or 'Failed to clear completed cases',
                notificationType = 'error'
            })
        end
    end)
    cb('ok')
end)

-- Filter records callback
RegisterNUICallback('filterRecords', function(data, cb)
    if not Config.SaveCasesToDatabase then
        SendNUIMessage({
            type = 'updateRecords',
            records = {}
        })
        cb('ok')
        return
    end

    -- Request filtered cases from server
    QBCore.Functions.TriggerCallback('tiger_law:server:getAllCases', function(result)
        if result.success then
            local filteredCases = result.cases or {}

            -- Filter by status if not "all"
            if data.status and data.status ~= "all" then
                local filtered = {}
                for _, case in pairs(filteredCases) do
                    if case.status == data.status then
                        table.insert(filtered, case)
                    end
                end
                filteredCases = filtered
            end

            SendNUIMessage({
                type = 'updateRecords',
                records = filteredCases
            })
        end
    end, data)
    cb('ok')
end)



-- Function to check if player can manage cases
function CanManageCases()
    local Player = QBCore.Functions.GetPlayerData()
    if not Player or not Player.job then return false end

    local job = Player.job.name
    local grade = Player.job.grade.level

    -- Check if job is in the allowed list
    if Config.CaseManagerJobs[job] then
        -- If it's a table, check for rank requirements
        if type(Config.CaseManagerJobs[job]) == 'table' then
            if grade >= Config.CaseManagerJobs[job].rank then
                return true
            end
        else
            -- If it's just set to true, allow access
            return true
        end
    end

    return false
end

-- Function to create court blip on the map
function CreateCourtBlip()
    -- Check if blip is enabled in config
    if not Config.CourtBlip or not Config.CourtBlip.enabled then
        return
    end

    -- Remove existing blip if it exists
    if courtBlip then
        RemoveBlip(courtBlip)
    end

    -- Create new blip
    courtBlip = AddBlipForCoord(
        Config.CourtBlip.coords.x,
        Config.CourtBlip.coords.y,
        Config.CourtBlip.coords.z
    )

    -- Set blip properties
    SetBlipSprite(courtBlip, Config.CourtBlip.sprite)
    SetBlipDisplay(courtBlip, Config.CourtBlip.display)
    SetBlipScale(courtBlip, Config.CourtBlip.scale)
    SetBlipColour(courtBlip, Config.CourtBlip.color)
    SetBlipAsShortRange(courtBlip, Config.CourtBlip.shortRange)

    -- Add blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.CourtBlip.name)
    EndTextCommandSetBlipName(courtBlip)
end

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary: 139 69 255;
    --primary-rgb: 139, 69, 255;
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --radius: 0.5rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    scrollbar-width: thin;
    scrollbar-color: rgba(var(--primary-rgb), 0.5) transparent;
    color: #eee;
}

*::-webkit-scrollbar {
    width: 8px;
}

*::-webkit-scrollbar-track {
    background: transparent;
}

*::-webkit-scrollbar-thumb {
    background: rgba(var(--primary-rgb), 0.5);
    border-radius: 10px;
}

body {
    font-family: 'Inter', sans-serif;
    background: transparent;
    color: #eee;
    overflow: hidden;
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
}

.hidden {
    display: none !important;
}

#container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1280px;
    height: 720px;
    background: rgba(10, 10, 10, 0.99);
    border-radius: 1rem;
    display: flex;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(var(--primary-rgb), 0.35);
    z-index: 9999;
    background-image: url("data:image/svg+xml,<svg id='patternId' width='100%' height='100%' xmlns='http://www.w3.org/2000/svg'><defs><pattern id='a' patternUnits='userSpaceOnUse' width='23' height='23' patternTransform='scale(2) rotate(0)'><rect x='0' y='0' width='100%' height='100%' fill='%232b2b3100'/><path d='M10-6V6M10 14v12M26 10H14M6 10H-6' transform='translate(1.5,0)' stroke-linecap='square' stroke-width='0.5' stroke='rgba(40, 180, 90, 0.05)' fill='none'/></pattern></defs><rect width='800%' height='800%' transform='translate(-32,-24)' fill='url(%23a)'/></svg>");
}

/* Navigation Sidebar */
.sidebar {
    width: 12%;
    min-width: 110px;
    max-width: 6.5%;
    background: rgba(38, 38, 38, 0.5);
    border-radius: 1rem 0 0 1rem;
    padding: 1.5rem 0;
    border-right: 1px solid rgba(64, 64, 64, 0.4);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.sidebar-item {
    width: 100%;
    min-width: 110px;
    max-width: 6.5%;
    aspect-ratio: 1 / 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    cursor: pointer;
    position: relative;
    border-radius: 0.75rem;
    background: rgba(10, 10, 10, 0.5);
    opacity: 0.8;
}

.sidebar-item.active {
    background: rgba(10, 10, 10, 1);
    opacity: 1;
}

.sidebar-item i {
    font-size: 1.875rem;
    color: rgba(255, 255, 255, 0.75);
}

.sidebar-item.active i {
    color: rgb(var(--primary-rgb));
}

.sidebar-item i.blur {
    position: absolute;
    top: 1.75rem;
    font-size: 1.875rem;
    color: rgba(var(--primary-rgb), 0.95);
    filter: blur(16px);
}

.sidebar-item span {
    font-size: 0.875rem;
    font-weight: 700;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    text-transform: capitalize;
}

.sidebar-item.active span {
    color: rgba(255, 255, 255, 0.95);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(64, 64, 64, 0.4);
}

.header h1 {
    font-size: 2rem;
    font-weight: 800;
    color: rgb(var(--primary-rgb));
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.date-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(64, 64, 64, 0.35);
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(var(--primary-rgb), 0.15);
}

.date-display i {
    color: rgb(var(--primary-rgb));
    font-size: 1.125rem;
}

.close-btn {
    background: #2a2a2a;
    color: white;
    border: 1px solid #444;
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    position: relative;
}

.close-btn .esc-key {
    background: #8b45ff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: 0.5rem;
    box-shadow: 0 2px 4px rgba(139, 69, 255, 0.3);
}

/* Tab Content */
.tab-content {
    display: none;
    flex: 1;
    overflow-y: auto;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

/* Submit Case Content */
.submit-case-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-right: 1rem;
    padding-right: 0.5rem;
}

.submit-case-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: rgb(var(--primary-rgb));
    margin-bottom: 0.5rem;
}

.submit-case-header p {
    color: rgba(255, 255, 255, 0.65);
    font-size: 0.875rem;
    line-height: 1.5;
}

.form-container {
    background: rgba(64, 64, 64, 0.35);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid rgba(64, 64, 64, 0.4);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(64, 64, 64, 0.5);
    border-radius: 0.5rem;
    color: white;
    font-size: 0.875rem;
    font-family: 'Inter', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: rgb(var(--primary-rgb));
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.submit-btn,
.clear-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    flex: 1;
    justify-content: center;
}

.submit-btn {
    background: linear-gradient(135deg, #8b45ff 0%, #7c3aed 100%);
    color: white;
}

.clear-btn {
    background: #2a2a2a;
    color: white;
    border: 1px solid #444;
}

/* My Cases Content */
.my-cases-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-right: 1rem;
    padding-right: 0.5rem;
}

.my-cases-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: rgb(var(--primary-rgb));
    margin-bottom: 0.5rem;
}

.my-cases-header p {
    color: rgba(255, 255, 255, 0.65);
    font-size: 0.875rem;
    line-height: 1.5;
}

.cases-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.case-card {
    background: rgba(64, 64, 64, 0.35);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(64, 64, 64, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
}

.case-card:hover {
    border-color: rgba(var(--primary-rgb), 0.5);
    transform: translateY(-2px);
}

.case-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.case-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
}

.case-id {
    background: rgba(var(--primary-rgb), 0.2);
    color: rgb(var(--primary-rgb));
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.case-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.case-detail {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.case-detail-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.case-detail-value {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.case-status {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-pending {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-reviewing {
    background: rgba(139, 69, 255, 0.2);
    color: rgb(var(--primary-rgb));
    border: 1px solid rgba(var(--primary-rgb), 0.3);
}

.status-approved {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-rejected {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Case Status Content */
.case-status-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-right: 1rem;
    padding-right: 0.5rem;
}

.case-status-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: rgb(var(--primary-rgb));
    margin-bottom: 0.5rem;
}

.case-status-header p {
    color: rgba(255, 255, 255, 0.65);
    font-size: 0.875rem;
    line-height: 1.5;
}

.status-form-container {
    background: rgba(64, 64, 64, 0.35);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid rgba(64, 64, 64, 0.4);
}

.check-btn {
    background: linear-gradient(135deg, #8b45ff 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.status-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.status-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
}

.approve-btn {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
}

.review-btn {
    background: linear-gradient(135deg, #8b45ff 0%, #7c3aed 100%);
    color: white;
}

.reject-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

/* Records Content */
.records-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-right: 1rem;
    padding-right: 0.5rem;
}

.records-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: rgb(var(--primary-rgb));
    margin-bottom: 0.5rem;
}

.records-header p {
    color: rgba(255, 255, 255, 0.65);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.records-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
}

.filter-group select {
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(64, 64, 64, 0.5);
    border-radius: 0.375rem;
    color: white;
    font-size: 0.875rem;
}

.clear-records-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.records-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: rgba(10, 10, 10, 0.95);
    border-radius: 1rem;
    width: 600px;
    max-width: 90vw;
    border: 2px solid rgba(var(--primary-rgb), 0.35);
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(64, 64, 64, 0.4);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: rgb(var(--primary-rgb));
}

.modal-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid rgba(64, 64, 64, 0.4);
}

.close-modal-btn {
    background: #2a2a2a;
    color: white;
    border: 1px solid #444;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
}

/* Notification System */
#notification-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 10001;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification {
    background: rgba(10, 10, 10, 0.95);
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(var(--primary-rgb), 0.35);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    border-color: rgba(34, 197, 94, 0.5);
}

.notification-success i {
    color: #22c55e;
}

.notification-error {
    border-color: rgba(239, 68, 68, 0.5);
}

.notification-error i {
    color: #ef4444;
}

.notification-info {
    border-color: rgba(var(--primary-rgb), 0.5);
}

.notification-info i {
    color: rgb(var(--primary-rgb));
}

.notification i {
    font-size: 1.25rem;
}

.notification span {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    flex: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Confirmation Modal Specific Styles */
.confirmation-modal .modal-content {
    width: 450px;
    max-width: 90vw;
}

.confirmation-modal .modal-header h3 {
    color: rgb(var(--primary-rgb));
    font-size: 1.25rem;
    font-weight: 600;
}

.confirmation-modal .modal-body p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    font-size: 0.95rem;
    margin: 0;
}

.confirmation-modal .modal-footer {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid rgba(64, 64, 64, 0.4);
}

.confirmation-modal .confirm-cancel {
    background: #2a2a2a;
    color: white;
    border: 1px solid #444;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    flex: 1;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.confirmation-modal .confirm-cancel:hover {
    background: #3a3a3a;
    border-color: #555;
}

.confirmation-modal .confirm-ok {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    flex: 1;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.confirmation-modal .confirm-ok:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 1400px) {
    #container {
        width: 95vw;
        height: 85vh;
    }
}

@media (max-width: 900px) {
    .sidebar {
        width: 150px;
        min-width: 150px;
    }

    .sidebar-item span {
        display: none;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .header-right {
        width: 100%;
        justify-content: space-between;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .case-details {
        grid-template-columns: 1fr;
    }

    .status-buttons {
        flex-direction: column;
    }

    .records-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

--  ██████╗ ███████╗██╗   ██╗    ██╗     █████╗ ██╗  ██╗███╗   ███╗███████╗██████╗ ████████╗██╗ ██████╗ ███████╗██████╗
--  ██╔══██╗██╔════╝██║   ██║    ██║    ██╔══██╗██║  ██║████╗ ████║██╔════╝██╔══██╗╚══██╔══╝██║██╔════╝ ██╔════╝██╔══██╗
--  ██║  ██║█████╗  ██║   ██║    ██║    ███████║███████║██╔████╔██║█████╗  ██║  ██║   ██║   ██║██║  ███╗█████╗  ██████╔╝
--  ██║  ██║██╔══╝  ╚██╗ ██╔╝    ██║    ██╔══██║██╔══██║██║╚██╔╝██║██╔══╝  ██║  ██║   ██║   ██║██║   ██║██╔══╝  ██╔══██╗
--  ██████╔╝███████╗ ╚████╔╝     ██║    ██║  ██║██║  ██║██║ ╚═╝ ██║███████╗██████╔╝   ██║   ██║╚██████╔╝███████╗██║  ██║
--  ╚═════╝ ╚══════╝  ╚═══╝      ╚═╝    ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝╚══════╝╚═════╝    ╚═╝   ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝

Config = {}

-- General Settings
Config.CommandName = 'lawtablet' -- Command to open the tablet
Config.ItemRequired = false -- Set to true if you want to require an item to open the tablet
Config.RequiredItem = 'lawtablet' -- Item name if ItemRequired is true

-- Permission Settings
Config.AllowAllPlayers = true -- Set to true to allow all players to use the tablet
Config.AllowedJobs = { -- Jobs that can use the tablet if AllowAllPlayers is false
    'lawyer',
    'judge',
    'police'
}

-- Jobs that can manage cases (approve, reject, etc.)
Config.CaseManagerJobs = {
    ['lawyer'] = true,
    ['judge'] = true,
    ['police'] = {
        ['rank'] = 4 -- Minimum rank required for police to manage cases
    }
}

-- Discord Webhook Settings
Config.UseDiscordWebhook = true -- Set to false to disable Discord webhook
Config.DiscordWebhooks = {
    ['default'] = 'https://ptb.discord.com/api/webhooks/1362936645044932829/0klGh_liGjchN2GD8ScP6BGezKgWC9qEl8QEo64VaFoQvj0iAAG_rbx_b4I5r1dNTpXw',
    ['complaint_civilian'] = 'https://ptb.discord.com/api/webhooks/1362936645044932829/0klGh_liGjchN2GD8ScP6BGezKgWC9qEl8QEo64VaFoQvj0iAAG_rbx_b4I5r1dNTpXw',
    ['complaint_police'] = 'https://ptb.discord.com/api/webhooks/1362936645044932829/0klGh_liGjchN2GD8ScP6BGezKgWC9qEl8QEo64VaFoQvj0iAAG_rbx_b4I5r1dNTpXw',
    ['summons'] = 'https://ptb.discord.com/api/webhooks/1362936645044932829/0klGh_liGjchN2GD8ScP6BGezKgWC9qEl8QEo64VaFoQvj0iAAG_rbx_b4I5r1dNTpXw',
    ['criminal_case'] = 'https://ptb.discord.com/api/webhooks/1362936645044932829/0klGh_liGjchN2GD8ScP6BGezKgWC9qEl8QEo64VaFoQvj0iAAG_rbx_b4I5r1dNTpXw',
    ['other'] = 'https://ptb.discord.com/api/webhooks/1362936645044932829/0klGh_liGjchN2GD8ScP6BGezKgWC9qEl8QEo64VaFoQvj0iAAG_rbx_b4I5r1dNTpXw'
}

-- Webhook Appearance
Config.WebhookName = 'Law InFiniTY CiTY'
Config.WebhookAvatar = 'https://i.ibb.co/cKwwCBPr/devbig.png' -- URL to avatar image

-- Case Types
Config.CaseTypes = {
    {label = 'Civilian Complaint', value = 'complaint_civilian'},
    {label = 'Police Complaint', value = 'complaint_police'},
    {label = 'Summons Request', value = 'summons'},
    {label = 'Criminal Case', value = 'criminal_case'},
    {label = 'Other', value = 'other'}
}

-- Database Settings
Config.SaveCasesToDatabase = true -- Set to false to disable database storage

-- UI Settings
Config.UISettings = {
    blur = true, -- Background blur when tablet is open
    statusBar = true, -- Show status bar with time, battery, etc.
    animations = true -- Enable animations
}

-- Court Blip Settings
Config.CourtBlip = {
    enabled = true, -- Set to false to disable the court blip
    coords = {x = -430.11, y = 1112.01, z = 329.8},
    sprite = 419, -- Blip sprite (icon) ID
    color = 5, -- Blip color
    scale = 1.0, -- Blip size
    display = 4, -- Display type
    shortRange = true, -- Only show when nearby
    name = "Court CiTY" -- Blip name on map
}
